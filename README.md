# LinkingBio - Core Application

This directory contains the core application for the **LinkingBio** platform, a modern, customizable bio-link platform built with Next.js 15, TypeScript, and Tailwind CSS. It allows users to create stunning, interactive profile pages with advanced customization options, analytics, and premium features.

### ✨ Showcase
visit web: [SOV Studio](https://sabicoder.xyz/projects/linking-bio)

![Preview](images/preview-image.png)

## Installation

```bash
pnpm install
```

## 🚀 Available Scripts

```bash
pnpm run dev          # Start development server
pnpm run build        # Build for production
pnpm run start        # Start production server
pnpm run lint         # Run ESLint
pnpm run test         # Run tests
pnpm run test:watch   # Run tests in watch mode
```

## 📝 Contributing

Contributions, issues, and feature requests are welcome!

Feel free to check the [issues page](https://github.com/NirussVn0/linkingbio/issues) for any open issues or to submit new ones.


## 📄 License

This project is [LICENSE](LICENSE.md)
Author [NirussVn0](https://github.com/NirussVn0)
- SOV Studio: [github](https://sabicoder.xyz/github)

