@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 0%;
    --foreground: 0 0% 100%;
    --primary: 120 100% 50%;
    --primary-foreground: 0 0% 0%;
    --secondary: 0 0% 10%;
    --secondary-foreground: 0 0% 100%;
    --muted: 0 0% 10%;
    --muted-foreground: 0 0% 60%;
    --accent: 120 100% 50%;
    --accent-foreground: 0 0% 0%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;
    --border: 120 100% 50%;
    --input: 0 0% 20%;
    --ring: 120 100% 50%;
  }
}

@layer utilities {
  .perspective-1000 {
    perspective: 1000px;
  }

  .preserve-3d {
    transform-style: preserve-3d;
  }

  .backface-hidden {
    backface-visibility: hidden;
  }

  .rotate-y-180 {
    transform: rotateY(180deg);
  }
}

body {
  background-color: black;
  color: white;
  overflow-x: hidden;
}

html {
  scroll-behavior: auto;
}

* {
  cursor: none !important;
}

.will-change-transform {
  will-change: transform;
}

@media (prefers-reduced-motion: reduce) {
  * {
    cursor: auto !important;
  }

  html {
    scroll-behavior: auto;
  }

  .will-change-transform {
    will-change: auto;
  }
}

/* Performance optimizations */
@media (max-width: 768px) {
  .will-change-transform {
    will-change: auto;
  }
}
